<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

// Set page variables for header
$pageTitle = 'Settings';
$current_page = basename($_SERVER['PHP_SELF']);

// Set session variables for header if not already set
if (!isset($_SESSION['admin_name'])) {
    $_SESSION['admin_name'] = $_SESSION['username'] ?? 'Admin';
}
if (!isset($_SESSION['admin_role'])) {
    $_SESSION['admin_role'] = $_SESSION['role'] ?? 'Administrator';
}
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = $_SESSION['user_id'] ?? null;
}

$message = '';
$messageType = '';

if ($_POST) {
    $database = new Database();
    $conn = $database->getConnection();
    
    try {
        // Update all settings
        foreach ($_POST as $key => $value) {
            if ($key !== 'submit') {
                $cleanValue = sanitizeInput($value);
                $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                $stmt->execute([$key, $cleanValue, $cleanValue]);
            }
        }
        
        $message = 'Settings updated successfully!';
        $messageType = 'success';
    } catch (PDOException $e) {
        $message = 'Database error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// Get current settings
$database = new Database();
$conn = $database->getConnection();
$stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings");
$stmt->execute();
$settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

include 'includes/admin_header.php';
?>

<!-- Settings Management Content -->
<div class="page-header d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">Settings</h2>
        <p class="text-muted mb-0">Configure your website settings and preferences.</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
            <i class="fas fa-undo"></i> Reset to Defaults
        </button>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
        <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<form method="POST">
                <!-- Company Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-building"></i> Company Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">Company Name</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="<?php echo htmlspecialchars($settings['company_name'] ?? ''); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_email" class="form-label">Company Email</label>
                                    <input type="email" class="form-control" id="company_email" name="company_email" 
                                           value="<?php echo htmlspecialchars($settings['company_email'] ?? ''); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_phone" class="form-label">Company Phone</label>
                                    <input type="text" class="form-control" id="company_phone" name="company_phone" 
                                           value="<?php echo htmlspecialchars($settings['company_phone'] ?? ''); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_mobile" class="form-label">Company Mobile</label>
                                    <input type="text" class="form-control" id="company_mobile" name="company_mobile" 
                                           value="<?php echo htmlspecialchars($settings['company_mobile'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_address" class="form-label">Company Address</label>
                                    <textarea class="form-control" id="company_address" name="company_address" rows="4"><?php echo htmlspecialchars($settings['company_address'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_description" class="form-label">Company Description</label>
                                    <textarea class="form-control" id="company_description" name="company_description" rows="4"><?php echo htmlspecialchars($settings['company_description'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media Links -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-share-alt"></i> Social Media Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="facebook_url" class="form-label">
                                        <i class="fab fa-facebook text-primary"></i> Facebook URL
                                    </label>
                                    <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                           value="<?php echo htmlspecialchars($settings['facebook_url'] ?? ''); ?>"
                                           placeholder="https://facebook.com/yourpage">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="instagram_url" class="form-label">
                                        <i class="fab fa-instagram text-danger"></i> Instagram URL
                                    </label>
                                    <input type="url" class="form-control" id="instagram_url" name="instagram_url" 
                                           value="<?php echo htmlspecialchars($settings['instagram_url'] ?? ''); ?>"
                                           placeholder="https://instagram.com/yourpage">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="linkedin_url" class="form-label">
                                        <i class="fab fa-linkedin text-info"></i> LinkedIn URL
                                    </label>
                                    <input type="url" class="form-control" id="linkedin_url" name="linkedin_url" 
                                           value="<?php echo htmlspecialchars($settings['linkedin_url'] ?? ''); ?>"
                                           placeholder="https://linkedin.com/company/yourcompany">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="youtube_url" class="form-label">
                                        <i class="fab fa-youtube text-danger"></i> YouTube URL
                                    </label>
                                    <input type="url" class="form-control" id="youtube_url" name="youtube_url" 
                                           value="<?php echo htmlspecialchars($settings['youtube_url'] ?? ''); ?>"
                                           placeholder="https://youtube.com/channel/yourchannel">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Website Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-globe"></i> Website Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_title" class="form-label">Site Title</label>
                                    <input type="text" class="form-control" id="site_title" name="site_title" 
                                           value="<?php echo htmlspecialchars($settings['site_title'] ?? ''); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="site_tagline" class="form-label">Site Tagline</label>
                                    <input type="text" class="form-control" id="site_tagline" name="site_tagline" 
                                           value="<?php echo htmlspecialchars($settings['site_tagline'] ?? ''); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contact_form_email" class="form-label">Contact Form Email</label>
                                    <input type="email" class="form-control" id="contact_form_email" name="contact_form_email" 
                                           value="<?php echo htmlspecialchars($settings['contact_form_email'] ?? ''); ?>">
                                    <div class="form-text">Email address to receive contact form submissions</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_description" class="form-label">Site Description</label>
                                    <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($settings['site_description'] ?? ''); ?></textarea>
                                    <div class="form-text">Used for SEO meta description</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="site_keywords" class="form-label">Site Keywords</label>
                                    <textarea class="form-control" id="site_keywords" name="site_keywords" rows="2"><?php echo htmlspecialchars($settings['site_keywords'] ?? ''); ?></textarea>
                                    <div class="form-text">Comma-separated keywords for SEO</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Business Hours -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock"></i> Business Hours</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="business_hours_weekdays" class="form-label">Weekdays (Mon-Fri)</label>
                                    <input type="text" class="form-control" id="business_hours_weekdays" name="business_hours_weekdays" 
                                           value="<?php echo htmlspecialchars($settings['business_hours_weekdays'] ?? ''); ?>"
                                           placeholder="8:00 AM - 6:00 PM">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="business_hours_weekend" class="form-label">Weekend (Sat-Sun)</label>
                                    <input type="text" class="form-control" id="business_hours_weekend" name="business_hours_weekend" 
                                           value="<?php echo htmlspecialchars($settings['business_hours_weekend'] ?? ''); ?>"
                                           placeholder="9:00 AM - 4:00 PM">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

    <div class="d-flex justify-content-end">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-save"></i> Save Settings
        </button>
    </div>
</form>

<script>
function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
        // Implement reset functionality
        alert('Reset functionality to be implemented');
    }
}

// Auto-save functionality (optional)
let saveTimeout;
document.querySelectorAll('input, textarea, select').forEach(element => {
    element.addEventListener('input', function() {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(() => {
            // Show auto-save indicator
            console.log('Auto-saving...');
        }, 2000);
    });
});
</script>

<?php include 'includes/admin_footer.php'; ?>
