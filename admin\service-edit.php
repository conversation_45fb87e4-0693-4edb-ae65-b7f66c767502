<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

// Set page variables for header
$pageTitle = 'Edit Service';
$current_page = basename($_SERVER['PHP_SELF']);

// Set session variables for header if not already set
if (!isset($_SESSION['admin_name'])) {
    $_SESSION['admin_name'] = $_SESSION['username'] ?? 'Admin';
}
if (!isset($_SESSION['admin_role'])) {
    $_SESSION['admin_role'] = $_SESSION['role'] ?? 'Administrator';
}
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = $_SESSION['user_id'] ?? null;
}

$pageTitle = 'Edit Service';

// Get service ID
$serviceId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$serviceId) {
    header('Location: services.php');
    exit();
}

// Get service data
$database = new Database();
$conn = $database->getConnection();
$stmt = $conn->prepare("SELECT * FROM services WHERE id = ?");
$stmt->execute([$serviceId]);
$service = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$service) {
    header('Location: services.php');
    exit();
}

$message = '';
$messageType = '';

if ($_POST) {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $icon = sanitizeInput($_POST['icon']);
    $status = sanitizeInput($_POST['status']);
    
    if ($title && $description) {
        // Handle service image upload
        $serviceImage = $service['image']; // Keep existing image
        if (isset($_FILES['service_image']) && $_FILES['service_image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['service_image'], '../uploads/');
            if ($uploadResult['success']) {
                // Delete old image if exists
                if ($service['image'] && file_exists('../' . $service['image'])) {
                    unlink('../' . $service['image']);
                }
                $serviceImage = $uploadResult['relative_path'];
            }
        }
        
        try {
            $stmt = $conn->prepare("UPDATE services SET title = ?, description = ?, image = ?, icon = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $stmt->execute([$title, $description, $serviceImage, $icon, $status, $serviceId]);
            
            $message = 'Service updated successfully!';
            $messageType = 'success';
            
            // Refresh service data
            $stmt = $conn->prepare("SELECT * FROM services WHERE id = ?");
            $stmt->execute([$serviceId]);
            $service = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    }
}

// Common FontAwesome icons for services
$commonIcons = [
    'fas fa-tools' => 'Tools',
    'fas fa-hard-hat' => 'Hard Hat',
    'fas fa-hammer' => 'Hammer',
    'fas fa-wrench' => 'Wrench',
    'fas fa-cogs' => 'Cogs',
    'fas fa-building' => 'Building',
    'fas fa-home' => 'Home',
    'fas fa-industry' => 'Industry',
    'fas fa-truck' => 'Truck',
    'fas fa-tractor' => 'Tractor',
    'fas fa-paint-roller' => 'Paint Roller',
    'fas fa-ruler-combined' => 'Ruler',
    'fas fa-drafting-compass' => 'Compass',
    'fas fa-layer-group' => 'Layers'
];

include 'includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Edit Service: <?php echo htmlspecialchars($service['title']); ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="services.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Services
                    </a>
                    <a href="../service-detail.php?id=<?php echo $service['id']; ?>" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-eye"></i> View Service
                    </a>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Edit Service Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Service Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Service Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo htmlspecialchars($service['title']); ?>" 
                                           placeholder="e.g., Civil Engineering" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description *</label>
                                    <textarea class="form-control" id="description" name="description" rows="6" 
                                              placeholder="Describe the service in detail..." required><?php echo htmlspecialchars($service['description']); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="icon" class="form-label">Icon</label>
                                            <select class="form-control" id="icon" name="icon" onchange="updateIconPreview()">
                                                <option value="">Select an icon</option>
                                                <?php foreach ($commonIcons as $iconClass => $iconName): ?>
                                                    <option value="<?php echo $iconClass; ?>" 
                                                            <?php echo $service['icon'] == $iconClass ? 'selected' : ''; ?>>
                                                        <?php echo $iconName; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">Choose an icon to represent this service</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-control" id="status" name="status">
                                                <option value="active" <?php echo $service['status'] == 'active' ? 'selected' : ''; ?>>Active</option>
                                                <option value="inactive" <?php echo $service['status'] == 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Icon Preview</label>
                                    <div class="text-center p-4 border rounded">
                                        <i id="iconPreview" class="<?php echo $service['icon'] ?: 'fas fa-tools'; ?> fa-3x text-primary"></i>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Current Service Image</label>
                                    <?php if ($service['image']): ?>
                                        <div class="current-image mb-3">
                                            <img src="../<?php echo htmlspecialchars($service['image']); ?>" 
                                                 alt="Current service image" 
                                                 style="max-width: 100%; max-height: 200px; border-radius: 8px;">
                                        </div>
                                    <?php else: ?>
                                        <div class="text-muted mb-3">No image uploaded</div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="service_image" class="form-label">Update Service Image</label>
                                    <input type="file" class="form-control" id="service_image" name="service_image" 
                                           accept="image/*" onchange="previewImage()">
                                    <div class="form-text">Upload a new service image (optional)</div>
                                    <div id="imagePreview" class="mt-3" style="display: none;">
                                        <img id="preview" style="max-width: 100%; max-height: 200px; border-radius: 8px;" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="services.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Service
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Service Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Service Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h4 class="text-primary"><?php echo $service['id']; ?></h4>
                                <small class="text-muted">Service ID</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h4 class="text-info"><?php echo date('M d, Y', strtotime($service['created_at'])); ?></h4>
                                <small class="text-muted">Created</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h4 class="text-warning"><?php echo $service['updated_at'] ? date('M d, Y', strtotime($service['updated_at'])) : 'Never'; ?></h4>
                                <small class="text-muted">Last Updated</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item text-center">
                                <h4 class="text-<?php echo $service['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                    <?php echo ucfirst($service['status']); ?>
                                </h4>
                                <small class="text-muted">Current Status</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function updateIconPreview() {
    const iconSelect = document.getElementById('icon');
    const iconPreview = document.getElementById('iconPreview');
    
    if (iconSelect.value) {
        iconPreview.className = iconSelect.value + ' fa-3x text-primary';
    } else {
        iconPreview.className = 'fas fa-tools fa-3x text-primary';
    }
}

function previewImage() {
    const fileInput = document.getElementById('service_image');
    const imagePreview = document.getElementById('imagePreview');
    const preview = document.getElementById('preview');
    
    if (fileInput.files && fileInput.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            imagePreview.style.display = 'block';
        };
        
        reader.readAsDataURL(fileInput.files[0]);
    } else {
        imagePreview.style.display = 'none';
    }
}

// Initialize icon preview
document.addEventListener('DOMContentLoaded', function() {
    updateIconPreview();
});
</script>

<style>
.stat-item {
    padding: 1rem;
    border-radius: 8px;
    background: #f8f9fa;
    margin-bottom: 1rem;
}

.current-image {
    border: 2px dashed #dee2e6;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}
</style>

<?php include 'includes/admin_footer.php'; ?>
