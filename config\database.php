<?php
// Database configuration
class Database {
    private $host = 'localhost';
    private $db_name = 'construction_website';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                )
            );
        } catch(PDOException $exception) {
            error_log("Database connection error: " . $exception->getMessage());
            // Don't echo the error in production, just log it
            return null;
        }

        return $this->conn;
    }
}

// Create database tables if they don't exist
function createTables() {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Users table
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'user') DEFAULT 'admin',
        first_name VARCHAR(100),
        last_name VARCHAR(100),
        phone VARCHAR(20),
        avatar VARCHAR(255),
        bio TEXT,
        last_login TIMESTAMP NULL,
        email_verified BOOLEAN DEFAULT FALSE,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);

    // Add new columns to existing users table if they don't exist
    $columns_to_add = [
        'first_name VARCHAR(100)',
        'last_name VARCHAR(100)',
        'phone VARCHAR(20)',
        'avatar VARCHAR(255)',
        'bio TEXT',
        'last_login TIMESTAMP NULL',
        'email_verified BOOLEAN DEFAULT FALSE',
        'status ENUM("active", "inactive") DEFAULT "active"',
        'updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];

    foreach ($columns_to_add as $column) {
        $column_name = explode(' ', $column)[0];
        try {
            $stmt = $conn->query("SHOW COLUMNS FROM users LIKE '$column_name'");
            if ($stmt->rowCount() == 0) {
                $conn->exec("ALTER TABLE users ADD COLUMN $column");
            }
        } catch (PDOException $e) {
            // Column might already exist or other error, continue
        }
    }
    
    // Services table
    $sql = "CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        image VARCHAR(255),
        icon VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    
    // Projects table
    $sql = "CREATE TABLE IF NOT EXISTS projects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        location VARCHAR(255),
        client VARCHAR(255),
        start_date DATE,
        end_date DATE,
        status ENUM('completed', 'ongoing', 'planned') DEFAULT 'ongoing',
        featured_image VARCHAR(255),
        gallery TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    
    // Media table
    $sql = "CREATE TABLE IF NOT EXISTS media (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255),
        filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_type ENUM('image', 'video') NOT NULL,
        file_size INT,
        alt_text VARCHAR(255),
        category VARCHAR(100),
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    
    // Messages table
    $sql = "CREATE TABLE IF NOT EXISTS messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        subject VARCHAR(255),
        message TEXT NOT NULL,
        status ENUM('new', 'read', 'replied') DEFAULT 'new',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    
    // Pages table for dynamic content
    $sql = "CREATE TABLE IF NOT EXISTS pages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        page_name VARCHAR(100) UNIQUE NOT NULL,
        title VARCHAR(255),
        content TEXT,
        meta_description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    
    // Settings table
    $sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    
    // Insert default admin user if not exists
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT), 'admin']);
    }
    
    // Insert default settings
    $defaultSettings = [
        ['company_name', 'Flori Construction Ltd'],
        ['company_email', '<EMAIL>'],
        ['company_phone', '0208 914 7883'],
        ['company_mobile', '078 8292 3621'],
        ['company_address', '662 High Road North Finchley, London N12 0NL'],
        ['facebook_url', 'https://www.facebook.com/FloriConstructionLtd'],
        ['instagram_url', 'https://www.instagram.com/flori_construction_ltd/'],
        ['youtube_url', 'https://www.youtube.com/@floriconstructionltd7045'],
        ['linkedin_url', 'https://www.linkedin.com/in/floriconstructionltd/']
    ];
    
    foreach ($defaultSettings as $setting) {
        $stmt = $conn->prepare("INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)");
        $stmt->execute($setting);
    }

    // Insert sample media if none exists
    $stmt = $conn->prepare("SELECT COUNT(*) FROM media");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $sampleMedia = [
            ['Construction Site Overview', 'construction-site-1.jpg', 'uploads/construction-site-1.jpg', 'image', 150000, 'Overview of our construction site', 'projects'],
            ['Foundation Work', 'foundation-work.jpg', 'uploads/foundation-work.jpg', 'image', 180000, 'Foundation construction in progress', 'groundworks'],
            ['RC Frame Construction', 'rc-frame.jpg', 'uploads/rc-frame.jpg', 'image', 200000, 'Reinforced concrete frame construction', 'rc-frames'],
            ['Basement Construction', 'basement.jpg', 'uploads/basement.jpg', 'image', 175000, 'Basement construction project', 'basements'],
            ['Landscaping Project', 'landscaping.jpg', 'uploads/landscaping.jpg', 'image', 160000, 'Hard landscaping project completion', 'landscaping'],
            ['Project Timelapse', 'project-timelapse.mp4', 'uploads/project-timelapse.mp4', 'video', 5000000, 'Timelapse of project construction', 'projects']
        ];

        foreach ($sampleMedia as $media) {
            $stmt = $conn->prepare("INSERT INTO media (title, filename, file_path, file_type, file_size, alt_text, category) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute($media);
        }
    }
}

// Initialize database
createTables();
?>
