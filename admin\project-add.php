<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

// Set page variables for header
$pageTitle = 'Add New Project';
$current_page = basename($_SERVER['PHP_SELF']);

// Set session variables for header if not already set
if (!isset($_SESSION['admin_name'])) {
    $_SESSION['admin_name'] = $_SESSION['username'] ?? 'Admin';
}
if (!isset($_SESSION['admin_role'])) {
    $_SESSION['admin_role'] = $_SESSION['role'] ?? 'Administrator';
}
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = $_SESSION['user_id'] ?? null;
}

$message = '';
$messageType = '';

if ($_POST) {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    $location = sanitizeInput($_POST['location']);
    $client = sanitizeInput($_POST['client']);
    $status = sanitizeInput($_POST['status']);
    $startDate = $_POST['start_date'] ?: null;
    $endDate = $_POST['end_date'] ?: null;
    
    if ($title && $description && $location && $status) {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Handle featured image upload
        $featuredImage = null;
        if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['featured_image'], '../uploads/');
            if ($uploadResult['success']) {
                $featuredImage = $uploadResult['relative_path'];
            }
        }
        
        try {
            $stmt = $conn->prepare("INSERT INTO projects (title, description, location, client, start_date, end_date, status, featured_image) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$title, $description, $location, $client, $startDate, $endDate, $status, $featuredImage]);
            
            $message = 'Project added successfully!';
            $messageType = 'success';
            
            // Clear form data
            $_POST = [];
        } catch (PDOException $e) {
            $message = 'Database error: ' . $e->getMessage();
            $messageType = 'error';
        }
    } else {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    }
}

include 'includes/admin_header.php';
?>

<!-- Add Project Content -->
<div class="page-header d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">Add New Project</h2>
        <p class="text-muted mb-0">Create a new construction project entry.</p>
    </div>
    <div class="d-flex gap-2">
        <a href="projects.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Projects
        </a>
    </div>
</div>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
        <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Add Project Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Project Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Project Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description *</label>
                                    <textarea class="form-control" id="description" name="description" rows="5" required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="location" class="form-label">Location *</label>
                                            <input type="text" class="form-control" id="location" name="location" 
                                                   value="<?php echo isset($_POST['location']) ? htmlspecialchars($_POST['location']) : ''; ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="client" class="form-label">Client</label>
                                            <input type="text" class="form-control" id="client" name="client" 
                                                   value="<?php echo isset($_POST['client']) ? htmlspecialchars($_POST['client']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status *</label>
                                            <select class="form-control" id="status" name="status" required>
                                                <option value="">Select Status</option>
                                                <option value="planned" <?php echo (isset($_POST['status']) && $_POST['status'] == 'planned') ? 'selected' : ''; ?>>Planned</option>
                                                <option value="ongoing" <?php echo (isset($_POST['status']) && $_POST['status'] == 'ongoing') ? 'selected' : ''; ?>>Ongoing</option>
                                                <option value="completed" <?php echo (isset($_POST['status']) && $_POST['status'] == 'completed') ? 'selected' : ''; ?>>Completed</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="start_date" class="form-label">Start Date</label>
                                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                                   value="<?php echo isset($_POST['start_date']) ? $_POST['start_date'] : ''; ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="end_date" class="form-label">End Date</label>
                                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                                   value="<?php echo isset($_POST['end_date']) ? $_POST['end_date'] : ''; ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="featured_image" class="form-label">Featured Image</label>
                                    <input type="file" class="form-control" id="featured_image" name="featured_image" 
                                           accept="image/*" onchange="previewImage()">
                                    <div class="form-text">Upload a featured image for this project</div>
                                    <div id="imagePreview" class="mt-3" style="display: none;">
                                        <img id="preview" style="max-width: 100%; max-height: 200px; border-radius: 8px;" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="projects.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Project
                            </button>
                        </div>
                    </form>
    </div>
</div>

<script>
function previewImage() {
    const fileInput = document.getElementById('featured_image');
    const imagePreview = document.getElementById('imagePreview');
    const preview = document.getElementById('preview');
    
    if (fileInput.files && fileInput.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            imagePreview.style.display = 'block';
        };
        
        reader.readAsDataURL(fileInput.files[0]);
    } else {
        imagePreview.style.display = 'none';
    }
}
</script>

<?php include 'includes/admin_footer.php'; ?>
