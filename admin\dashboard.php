<?php
require_once __DIR__ . '/../includes/functions.php';
requireLogin();

// Set page variables for header
$pageTitle = 'Dashboard';
$current_page = basename($_SERVER['PHP_SELF']);

// Set session variables for header if not already set
if (!isset($_SESSION['admin_name'])) {
    $_SESSION['admin_name'] = $_SESSION['username'] ?? 'Admin';
}
if (!isset($_SESSION['admin_role'])) {
    $_SESSION['admin_role'] = $_SESSION['role'] ?? 'Administrator';
}
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = $_SESSION['user_id'] ?? null;
}

// Get dashboard statistics
$database = new Database();
$conn = $database->getConnection();

$stats = [];
try {
    if ($conn) {
        $stats['total_projects'] = $conn->query("SELECT COUNT(*) FROM projects")->fetchColumn();
        $stats['completed_projects'] = $conn->query("SELECT COUNT(*) FROM projects WHERE status = 'completed'")->fetchColumn();
        $stats['ongoing_projects'] = $conn->query("SELECT COUNT(*) FROM projects WHERE status = 'ongoing'")->fetchColumn();
        $stats['total_services'] = $conn->query("SELECT COUNT(*) FROM services WHERE status = 'active'")->fetchColumn();
        $stats['total_media'] = $conn->query("SELECT COUNT(*) FROM media")->fetchColumn();
        $stats['new_messages'] = $conn->query("SELECT COUNT(*) FROM messages WHERE status = 'new'")->fetchColumn();
    } else {
        // Default values if database connection fails
        $stats = [
            'total_projects' => 0,
            'completed_projects' => 0,
            'ongoing_projects' => 0,
            'total_services' => 0,
            'total_media' => 0,
            'new_messages' => 0
        ];
    }
} catch (Exception $e) {
    error_log("Dashboard stats error: " . $e->getMessage());
    // Default values if query fails
    $stats = [
        'total_projects' => 0,
        'completed_projects' => 0,
        'ongoing_projects' => 0,
        'total_services' => 0,
        'total_media' => 0,
        'new_messages' => 0
    ];
}

// Recent activities
try {
    $recentProjects = getProjects(null, 5);
    $recentMessages = getMessages('new');
    $recentMessages = array_slice($recentMessages, 0, 5);
} catch (Exception $e) {
    error_log("Dashboard recent activities error: " . $e->getMessage());
    $recentProjects = [];
    $recentMessages = [];
}

include 'includes/admin_header.php';
?>

<!-- Dashboard Content -->
<div class="page-header d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">Dashboard</h2>
        <p class="text-muted mb-0">Welcome back! Here's what's happening with your projects.</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-secondary">
            <i class="fas fa-download"></i> Export
        </button>
        <a href="project-add.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Project
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-icon bg-primary-light text-primary">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['total_projects']; ?></div>
                    <div class="stats-label">Total Projects</div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-icon bg-success-light text-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['completed_projects']; ?></div>
                    <div class="stats-label">Completed Projects</div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-icon bg-info-light text-info">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['total_services']; ?></div>
                    <div class="stats-label">Active Services</div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-icon bg-warning-light text-warning">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['new_messages']; ?></div>
                    <div class="stats-label">New Messages</div>
                </div>
            </div>
</div>

<!-- Recent Projects and Messages -->
<div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Projects</h5>
                        <a href="projects.php" class="btn btn-sm btn-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentProjects)): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Project</th>
                                            <th>Location</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentProjects as $project): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($project['title']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($project['location']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $project['status'] == 'completed' ? 'success' : ($project['status'] == 'ongoing' ? 'warning' : 'info'); ?>">
                                                    <?php echo ucfirst($project['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($project['created_at'])); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                <h5>No Projects Yet</h5>
                                <p class="text-muted">Start by adding your first project.</p>
                                <a href="project-add.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add Project
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">New Messages</h5>
                        <a href="messages.php" class="btn btn-sm btn-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentMessages)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                                <h6>No New Messages</h6>
                                <p class="text-muted small">All caught up!</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recentMessages as $message): ?>
                            <div class="d-flex align-items-start mb-3 pb-3 border-bottom">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary-light text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($message['name']); ?></h6>
                                    <p class="mb-1 small text-muted"><?php echo htmlspecialchars($message['email']); ?></p>
                                    <p class="mb-1 small"><?php echo htmlspecialchars(substr($message['message'], 0, 80)) . '...'; ?></p>
                                    <small class="text-muted"><?php echo date('M j, g:i A', strtotime($message['created_at'])); ?></small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
</div>

<?php include 'includes/admin_footer.php'; ?>
