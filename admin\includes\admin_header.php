
<?php
// Set default values if not already set
if (!isset($pageTitle)) {
    $pageTitle = 'Dashboard';
}
if (!isset($current_page)) {
    $current_page = basename($_SERVER['PHP_SELF']);
}
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Admin Panel - Flori Construction</title>
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">
    
    <!-- Font Awesome 6.5 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" crossorigin="anonymous">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" crossorigin="anonymous">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" crossorigin="anonymous">
    
    <!-- Custom Admin CSS -->
    <link rel="stylesheet" href="assets/css/custom_admin.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <div class="admin-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <!-- Sidebar Brand -->
            <div class="sidebar-brand">
                <div class="brand-icon">
                    <i class="fas fa-hammer"></i>
                </div>
                <div class="brand-text">
                    <h5>Flori Construction</h5>
                    <small>Admin Panel</small>
                </div>
            </div>
            
            <!-- Sidebar Navigation -->
            <div class="sidebar-nav">
                <!-- Main Navigation -->
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="dashboard.php" class="nav-link <?php echo ($current_page == 'dashboard.php') ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </div>
                
                <!-- Projects Section -->
                <div class="nav-section">
                    <div class="nav-section-title">Projects</div>
                    <a href="projects.php" class="nav-link <?php echo ($current_page == 'projects.php') ? 'active' : ''; ?>">
                        <i class="fas fa-building"></i>
                        All Projects
                        <?php
                        // Get project count (you can implement this)
                        $project_count = 0; // Replace with actual count
                        if ($project_count > 0) {
                            echo '<span class="nav-badge badge bg-primary">' . $project_count . '</span>';
                        }
                        ?>
                    </a>
                    <a href="project-add.php" class="nav-link <?php echo ($current_page == 'project-add.php') ? 'active' : ''; ?>">
                        <i class="fas fa-plus"></i>
                        Add Project
                    </a>
                </div>
                
                <!-- Services Section -->
                <div class="nav-section">
                    <div class="nav-section-title">Services</div>
                    <a href="services.php" class="nav-link <?php echo ($current_page == 'services.php') ? 'active' : ''; ?>">
                        <i class="fas fa-tools"></i>
                        All Services
                    </a>
                    <a href="service-add.php" class="nav-link <?php echo ($current_page == 'service-add.php') ? 'active' : ''; ?>">
                        <i class="fas fa-plus"></i>
                        Add Service
                    </a>
                </div>
                
                <!-- Content Section -->
                <div class="nav-section">
                    <div class="nav-section-title">Content</div>
                    <a href="media.php" class="nav-link <?php echo ($current_page == 'media.php') ? 'active' : ''; ?>">
                        <i class="fas fa-images"></i>
                        Media Library
                    </a>
                    <a href="messages.php" class="nav-link <?php echo ($current_page == 'messages.php') ? 'active' : ''; ?>">
                        <i class="fas fa-envelope"></i>
                        Messages
                        <?php
                        // Get unread message count (you can implement this)
                        $unread_count = 0; // Replace with actual count
                        if ($unread_count > 0) {
                            echo '<span class="nav-badge badge bg-danger">' . $unread_count . '</span>';
                        }
                        ?>
                    </a>
                </div>
                
                <!-- System Section -->
                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a href="users.php" class="nav-link <?php echo ($current_page == 'users.php') ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i>
                        Users
                    </a>
                    <a href="settings.php" class="nav-link <?php echo ($current_page == 'settings.php') ? 'active' : ''; ?>">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                    <a href="profile.php" class="nav-link <?php echo ($current_page == 'profile.php') ? 'active' : ''; ?>">
                        <i class="fas fa-user"></i>
                        Profile
                    </a>
                </div>
            </div>
        </nav>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Header -->
            <header class="top-header">
                <div class="header-content">
                    <!-- Left Section -->
                    <div class="header-left">
                        <!-- Mobile Toggle -->
                        <button class="mobile-toggle" type="button" onclick="AdminPanel.toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                        
                        <!-- Page Title -->
                        <div>
                            <h1 class="page-title"><?php echo isset($pageTitle) ? $pageTitle : 'Dashboard'; ?></h1>
                            <?php if (isset($breadcrumb)): ?>
                                <div class="breadcrumb"><?php echo $breadcrumb; ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Right Section -->
                    <div class="header-right">
                        <!-- Search Box -->
                        <div class="search-box">
                            <div class="search-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <input type="text" class="search-input" placeholder="Search..." id="globalSearch">
                        </div>
                        
                        <!-- Header Actions -->
                        <div class="header-actions">
                            <!-- Notifications -->
                            <a href="#" class="action-btn" data-bs-toggle="tooltip" title="Notifications">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="notification-badge" style="display: none;">0</span>
                            </a>
                            
                            <!-- Messages -->
                            <a href="messages.php" class="action-btn" data-bs-toggle="tooltip" title="Messages">
                                <i class="fas fa-envelope"></i>
                                <span class="notification-badge" id="message-badge" style="display: none;">0</span>
                            </a>
                            
                            <!-- Quick Add -->
                            <div class="dropdown">
                                <button class="action-btn" type="button" data-bs-toggle="dropdown" data-bs-toggle="tooltip" title="Quick Add">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="project-add.php"><i class="fas fa-building"></i> New Project</a></li>
                                    <li><a class="dropdown-item" href="service-add.php"><i class="fas fa-tools"></i> New Service</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="media-upload.php"><i class="fas fa-upload"></i> Upload Media</a></li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- User Dropdown -->
                        <div class="user-dropdown dropdown">
                            <a href="#" class="user-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <?php
                                $admin_name = $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
                                $admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';
                                $admin_avatar = $_SESSION['admin_avatar'] ?? '';
                                ?>
                                <?php if (!empty($admin_avatar)): ?>
                                    <img src="<?php echo htmlspecialchars($admin_avatar); ?>" alt="User Avatar" class="user-avatar">
                                <?php else: ?>
                                    <div class="user-avatar" style="background: linear-gradient(135deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; width: 40px; height: 40px; border-radius: 50%;">
                                        <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                                    </div>
                                <?php endif; ?>
                                <div class="user-info">
                                    <div class="user-name"><?php echo htmlspecialchars($admin_name); ?></div>
                                    <div class="user-role"><?php echo htmlspecialchars(ucfirst($admin_role)); ?></div>
                                </div>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </a>
                            <ul class="dropdown-menu">
                                <li><h6 class="dropdown-header">Account</h6></li>
                                <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> Profile</a></li>
                                <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../index.php" target="_blank"><i class="fas fa-external-link-alt"></i> View Website</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Content Area -->
            <main class="content-area">
                <!-- Alert Messages -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php 
                        echo htmlspecialchars($_SESSION['success_message']); 
                        unset($_SESSION['success_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php 
                        echo htmlspecialchars($_SESSION['error_message']); 
                        unset($_SESSION['error_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['warning_message'])): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php 
                        echo htmlspecialchars($_SESSION['warning_message']); 
                        unset($_SESSION['warning_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['info_message'])): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php 
                        echo htmlspecialchars($_SESSION['info_message']); 
                        unset($_SESSION['info_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
